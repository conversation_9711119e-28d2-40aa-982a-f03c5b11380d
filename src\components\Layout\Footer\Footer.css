/* ===== FOOTER STYLES ===== */
/*xanh đậm 1 #2753d0
xanh đậm 2 #086ce4
xanh đậm 3 #0283f5  */

.footer {
  background: #fff;
  background-color: #0283f560;
  color: #fff; /* <PERSON><PERSON><PERSON> chữ xanh đậm */
  padding: var(--spacing-3xl) 0 var(--spacing-lg);
  font-family: "Roboto", Arial, sans-serif;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  color: #184d63;
}

/* ===== FOOTER LOGO ===== */
.footer-logo {
  flex: 0 0 100%;
  max-width: 300px;
  margin-bottom: var(--spacing-xl);
  color: #00b9f2; /* Màu xanh logo */
}

.footer-logo .logo-link {
  color: #00b9f2;
  margin-bottom: var(--spacing-md);
  font-weight: bold;
  font-size: 2.2rem;
  letter-spacing: 1px;
}

.footer-logo .logo-icon {
  color: var(--primary-color);
}

.footer-description {
  font-size: 14px;
  opacity: 0.8;
  margin-top: var(--spacing-md);
  color: #184d63;
}

/* ===== FOOTER LINKS ===== */
.footer-links {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  color: #184d63;
}

.footer-column {
  flex: 0 0 30%;
  margin-bottom: var(--spacing-xl);
  color: #184d63;
}

.footer-column h3 {
  font-size: 18px;
  margin-bottom: var(--spacing-lg);
  color: #008fd3; /* Màu xanh tiêu đề */
  /* position: relative; */
  font-weight: bold;
}

.footer-column h3::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--gradient-primary);
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #184d63;
}

.footer-column ul li {
  margin-bottom: var(--spacing-sm);
  color: #184d63;
}

.footer-column ul li a {
  color: #184d63;
  text-decoration: none;
  font-size: 15px;
  opacity: 0.9;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.footer-column ul li a:hover {
  opacity: 1;
  color: #00b9f2;
  font-weight: bold;
}

/* ===== CONTACT INFO ===== */
.contact-info li {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  color: #184d63;
}

.contact-info .icon {
  margin-right: var(--spacing-sm);
  opacity: 0.8;
  color: #184d63;
  font-size: 1.1em;
}

/* ===== FOOTER BOTTOM ===== */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(0, 185, 242, 0.1);
  color: #184d63;
}

.footer-bottom p {
  font-size: 14px;
  opacity: 0.8;
  color: #184d63;
}

/* ===== SOCIAL LINKS ===== */
.social-links {
  display: flex;
  gap: var(--spacing-sm);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: rgba(0, 185, 242, 0.08);
  border-radius: var(--radius-full);
  color: #00b9f2;
  text-decoration: none;
  font-size: 15px;
  transition: all var(--transition-normal);
}

.social-link:hover {
  background: #00b9f2;
  color: #fff;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 992px) {
  .footer-column {
    flex: 0 0 45%;
  }
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
  }

  .footer-links {
    flex-direction: column;
  }

  .footer-column {
    flex: 0 0 100%;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
}
