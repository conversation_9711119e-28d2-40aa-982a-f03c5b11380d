.gradient-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to top, #086ce4, #2753d0);
  color: white;
  border-radius: 15px;
  padding: 10px 18px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  /* transition: all 1s ease; */
  transition: background 0.2s ease-in-out, transform 0.2s ease-in-out;
  height: 40px;
  border: none;
  gap: 8px;
}

/* Biến thể: compact */
.btn-compact {
  width: auto !important;
  min-width: 120px;
}

/* Biến thể: full-width */
.btn-full {
  width: 100% !important;
}

/* Biến thể: danger */
.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

/* Hover */
.gradient-button:hover {
  transform: translateY(-1px);
  /* box-shadow: 0 6px 8px rgba(0, 181, 241, 0.3); */
  background: linear-gradient(to top, #0283f5, #086ce4);
}

.login-btn {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  justify-content: center;
  color: white !important;
  transition: color 0.3s ease;
}

/* Khi hover vào button, đổi màu text thành xanh lá */
.gradient-button:hover .login-btn {
  color: #2753d0;
}

.gradient-button:hover .exit {
  color: #ffffff;
}
.gradient-button .exit-ok {
  color: #ffffff;
}
.exit-btn:hover {
  background: linear-gradient(to top, #0283f5, #086ce4) !important;
  color: #fff;
  border: none;
}
.gradient-button:hover .login-title {
  color: #ffffff !important;
}
.exit-ok-btn {
  background: linear-gradient(to bottom, #f52f88, #ff4699);
  color: #fff;
  border: none;
}
.exit-ok-btn:hover {
  background: linear-gradient(90deg, #f52f88, #ff4699) !important;
  color: #fff;
}
/* Restrict to specific context to avoid affecting other buttons */
.gradient-button-container .ant-btn-variant-outlined:hover,
.auth-buttons .ant-btn-variant-outlined:hover {
  color: white !important;
  background: #ff4d4f !important;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px inset,
    rgba(243, 160, 160, 0.3) 0px 18px 36px -18px inset !important;
}
