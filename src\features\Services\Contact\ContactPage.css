/* Contact Page Styles - Renamed to avoid conflicts with header */
.contact-page-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 50%, #faf0ff 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  line-height: 1.6;
  color: #333;
  padding: 2rem 0;
}

.contact-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header Section */
.contact-header-section {
  text-align: center;
  margin-bottom: 3rem;
}

.contact-header-icons {
  justify-content: center;
  margin-bottom: 1rem;
}

.contact-icon-heart,
.contact-icon-shield {
  font-size: 2rem;
  color: #1890ff;
}

.contact-main-title {
  font-size: 2.5rem !important;
  font-weight: bold !important;
  color: #1f2937 !important;
  margin-bottom: 1rem !important;
  text-align: center;
}

.contact-main-description {
  font-size: 1.125rem !important;
  color: #6b7280 !important;
  max-width: 48rem;
  margin: 0 auto !important;
  line-height: 1.7 !important;
  text-align: center;
}

/* Content Grid */
.contact-content-grid {
  margin-bottom: 3rem;
}

/* Card Styles */
.contact-info-card,
.contact-form-card,
.contact-privacy-card,
.contact-features-card {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  overflow: hidden;
}

.contact-card-header {
  padding: 1.5rem 1.5rem 0;
}

.contact-card-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 0.5rem !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-title-icon {
  color: #1890ff;
  font-size: 1.25rem;
}

.contact-card-description {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  margin-bottom: 1.5rem !important;
}

/* Contact Info Styles */
.contact-info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.contact-info-item:last-child {
  margin-bottom: 0;
}

.contact-info-icon {
  font-size: 1.25rem;
  margin-top: 0.25rem;
  flex-shrink: 0;
  color: #1890ff;
}

.contact-info-details h5 {
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 0.25rem !important;
}

.contact-info-details p {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.125rem !important;
}

.contact-emergency-support {
  color: #059669 !important;
  font-weight: 500 !important;
  margin-top: 0.25rem !important;
}

/* Privacy Card */

.contact-privacy-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
}

.contact-privacy-icon {
  font-size: 1.5rem;
  margin-top: 0.25rem;
  flex-shrink: 0;
  color: #1890ff;
}

.contact-privacy-content h4 {
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 0.5rem !important;
}

.contact-privacy-content p {
  font-size: 0.875rem !important;
  line-height: 1.6 !important;
  margin-bottom: 0 !important;
}

/* Form Styles */
.contact-form {
  padding: 1.5rem;
}

.contact-form .ant-form-item-label > label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.contact-form .ant-input,
.contact-form .ant-select-selector,
.contact-form .ant-input-affix-wrapper {
  border-radius: 8px !important;
  border: 2px solid #d1d5db !important;
  transition: all 0.2s ease !important;
}

.contact-form .ant-input:focus,
.contact-form .ant-select-focused .ant-select-selector,
.contact-form .ant-input-affix-wrapper:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.contact-form .ant-form-item-has-error .ant-input,
.contact-form .ant-form-item-has-error .ant-select-selector,
.contact-form .ant-form-item-has-error .ant-input-affix-wrapper {
  border-color: #ef4444 !important;
}

.contact-submit-button {
  background: linear-gradient(135deg, #3b82f6 0%, #5695fa 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  height: 48px !important;
}

.contact-submit-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #508ff6 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Success Alert */
.contact-success-alert {
  margin: 1.5rem;
  border-radius: 8px !important;
}

/* Additional Info */
.contact-additional-info {
  margin-top: 3rem;
}

.contact-features-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  text-align: center;
  margin-bottom: 1.5rem !important;
}

.contact-features-grid {
  padding: 0 1.5rem 1.5rem;
}

.contact-feature-item {
  text-align: center;
  padding: 1rem;
}

.contact-feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
  color: #1890ff;
}

.contact-feature-title {
  font-weight: 500 !important;
  color: #1f2937 !important;
  margin-bottom: 0.25rem !important;
}

.contact-feature-description {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
  margin-bottom: 0 !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-features-grid {
    padding: 0 1rem 1rem;
  }
}

@media (max-width: 768px) {
  .contact-page-container {
    padding: 0 0.5rem;
  }

  .contact-main-title {
    font-size: 2rem !important;
  }

  .contact-main-description {
    font-size: 1rem !important;
  }

  .contact-card-header,
  .contact-form {
    padding: 1rem;
  }

  .contact-feature-item {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .contact-main-title {
    font-size: 1.75rem !important;
  }

  .contact-info-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
  }

  .contact-privacy-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}
