/* Articles Section */
.articles.section {
  background: #ffffff;
  padding: 32px 0;
}

/* Article Stats */
.article-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.sidebar-article-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
  font-size: 0.85rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.9rem;
}

.stat-item svg {
  flex-shrink: 0;
}

.stat-count {
  font-weight: 500;
  color: #333;
}

.like-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.like-button:hover {
  background-color: #ffe6e6;
  transform: scale(1.05);
}

.like-button:active {
  transform: scale(0.95);
}

.like-button.liking {
  opacity: 0.6;
  cursor: not-allowed;
}

.like-button.liking svg {
  animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Layout */
.blog-layout {
  display: flex;
  gap: 24px;
}

/* Featured Article (bài nổi bật bên trái) */
.featured-article {
  flex: 2;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  text-decoration: none;
  transition: box-shadow 0.2s, transform 0.2s;
}

.featured-article:hover {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  transform: translateY(-4px) scale(1.02);
}

.featured-image {
  width: 100%;
  height: 400px; /* hoặc tăng lên 300px, tuỳ ý */
  overflow: hidden;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ảnh sẽ phủ kín khung, không bị méo */
  display: block;
}

.featured-content {
  padding: 18px 22px 16px 22px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.featured-content h2 {
  font-size: 1.25rem;
  font-weight: bold;
  color: #184d63;
  margin-bottom: 10px;
  line-height: 1.3;
}

.featured-content p {
  color: #184d63;
  font-size: 1rem;
  margin-bottom: 14px;
}

/* Sidebar Articles (bên phải) */
.sidebar-articles {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.sidebar-article {
  background: #fff;
  border-radius: 16px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  display: flex;
  gap: 12px;
  text-decoration: none;
  transition: box-shadow 0.2s, transform 0.2s;
  overflow: hidden;
}

.sidebar-article:hover {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  transform: translateY(-2px) scale(1.01);
}

.sidebar-article-image img {
  width: 90px;
  height: 90px;
  object-fit: cover;
  border-radius: 12px 0 0 12px;
}

.sidebar-article-content {
  padding: 10px 10px 10px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.sidebar-article-content h3 {
  font-size: 1.05rem;
  font-weight: bold;
  color: #184d63;
  margin-bottom: 6px;
  line-height: 1.25;
}

/* Meta info */
.article-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.95rem;
  color: #7bbbe7;
}

.article-author {
  display: flex;
  align-items: center;
  gap: 6px;
}

.article-author img {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  object-fit: cover;
  background: #e0f4ff;
}

.article-date {
  color: #7bbbe7;
  font-size: 0.95rem;
}

/* Pagination dots */
.sidebar-pagination {
  display: flex;
  gap: 8px;
  margin: 12px 0 0 0;
  justify-content: center;
}

.pagination-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  background: #cbeafd;
  cursor: pointer;
  transition: background 0.2s;
}

.pagination-dot.active,
.pagination-dot:hover {
  background: #00b9f2;
}

/* Xem tất cả bài viết */
.see-all-button-blog {
  margin-top: 18px;
  text-align: center;
}

.view-all-link-blog {
  display: inline-block;
  padding: 8px 22px;
  border-radius: 22px;
  background: #0283f5;
  color: #fff;
  font-weight: bold;
  text-decoration: none;
  font-size: 1rem;
  transition: background 0.2s;
}

.view-all-link-blog:hover {
  background: #0283f5;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
    rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
}

/* Responsive */
@media (max-width: 1024px) {
  .blog-layout {
    flex-direction: column;
  }
  .featured-article,
  .sidebar-articles {
    width: 100%;
    flex: unset;
  }
  .sidebar-articles {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }
  .sidebar-article {
    width: 48%;
  }
}

@media (max-width: 700px) {
  .blog-layout {
    flex-direction: column;
    gap: 18px;
  }
  .sidebar-articles {
    flex-direction: column;
    gap: 12px;
  }
  .sidebar-article {
    width: 100%;
  }
}
