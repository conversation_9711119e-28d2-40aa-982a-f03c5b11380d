/* Medical Result Viewer Styles */
.medical-result-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
}

.medical-result-compact {
  border-left: 4px solid #1890ff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.medical-result-compact:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.medical-result-compact.severity-error {
  border-left-color: #ff4d4f;
}

.medical-result-compact.severity-warning {
  border-left-color: #faad14;
}

.medical-result-compact.severity-success {
  border-left-color: #52c41a;
}

.result-compact-header {
  margin-bottom: 12px;
}

.result-compact-value {
  margin-top: 8px;
}

.result-range-indicator {
  text-align: right;
}

.medical-result-full {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.result-header {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f0f0;
}

.result-content {
  padding: 0;
}

.lab-values-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
  border: 1px solid #e6f7ff;
}

.lab-values-card .ant-card-head {
  background: transparent;
  border-bottom: 1px solid #e6f7ff;
}

.result-status-indicator {
  text-align: center;
  padding: 8px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 6px;
}

/* Progress bar customization */
.ant-progress-line {
  margin-bottom: 0;
}

.ant-progress-bg {
  border-radius: 10px;
}

/* Severity-based styling */
.severity-error .ant-progress-bg {
  background: linear-gradient(90deg, #ff7875 0%, #ff4d4f 100%);
}

.severity-warning .ant-progress-bg {
  background: linear-gradient(90deg, #ffc53d 0%, #faad14 100%);
}

.severity-success .ant-progress-bg {
  background: linear-gradient(90deg, #73d13d 0%, #52c41a 100%);
}

/* Alert customization */
.ant-alert {
  border-radius: 8px;
  border-width: 1px;
}

.ant-alert-error {
  background: linear-gradient(135deg, #fff2f0 0%, #ffffff 100%);
  border-color: #ffccc7;
}

.ant-alert-warning {
  background: linear-gradient(135deg, #fffbe6 0%, #ffffff 100%);
  border-color: #ffe58f;
}

.ant-alert-success {
  background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
  border-color: #b7eb8f;
}

/* Card hover effects */
.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* Statistic customization */
.ant-statistic-title {
  font-weight: 500;
  color: #666;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ant-statistic-content {
  font-weight: 600;
}

/* Badge customization */
.ant-badge-status-dot {
  width: 8px;
  height: 8px;
}

/* Descriptions customization */
.ant-descriptions-item-label {
  font-weight: 500;
  color: #666;
}

.ant-descriptions-item-content {
  color: #262626;
}

/* Modal customization */
.ant-modal-header {
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

/* Button customization */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* Restrict to medical result viewer context only */
.medical-result-viewer .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .medical-result-full .ant-row {
    flex-direction: column;
  }

  .medical-result-full .ant-col {
    width: 100% !important;
    margin-bottom: 16px;
  }

  .result-header {
    padding: 12px 16px;
  }

  .result-header .ant-row {
    flex-direction: column;
    gap: 12px;
  }
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.medical-result-compact,
.medical-result-full {
  animation: fadeInUp 0.3s ease-out;
}

/* Print styles */
@media print {
  .medical-result-full {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .result-header .ant-btn {
    display: none;
  }

  .ant-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
