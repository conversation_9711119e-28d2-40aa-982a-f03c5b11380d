/* Layout Container */
.hospital-page-container {
  width: 100%;
  min-height: 100vh;
  background: white;
  margin: 0 auto;
  padding: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hospital-center-wrapper {
  width: 100%;
  display: block;
}

.container-layout {
  width: 100%;
  max-width: 1200px;
  min-width: 1000px;
  padding: 0 24px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Hero section */
.hospital-hero {
  width: 100%;
  padding: 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
}

.hospital-profile {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 24px;
}

.hospital-logo .logo-image {
  width: 85px;
  height: 85px;
  object-fit: contain;
  margin-right: 24px;
}

.hospital-info h1 {
  margin: 0;
  font-size: 28px;
}

.hospital-address {
  display: flex;
  align-items: center;
  margin: 8px 0;
  font-size: 16px;
  color: #555;
}

.hospital-address .icon {
  margin-right: 8px;
}

.hospital-actions .action-link {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.hospital-actions .action-link:hover {
  text-decoration: underline;
}

/* Navigation tabs */
.profile-nav-tabs {
  width: 100%;
  margin-top: 16px;
  border-bottom: 1px solid #eee;
}

.nav-tabs {
  display: flex;
  gap: 24px;
  font-size: 16px;
  list-style: none;
  flex-wrap: wrap;
}

.nav-tab {
  cursor: pointer;
  padding: 12px 0;
  color: #444;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.nav-tab:hover {
  color: #1890ff;
}

.nav-tab.active {
  border-bottom: 2px solid #0283f5;
  color: #0283f5;
}

/* Main Content */
.main-content {
  width: 100%;
  padding-bottom: 100px;
  box-sizing: border-box;
  min-height: 700px;
}

.left-column {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.right-column {
  display: none;
}

/* Section block */
.content-section {
  width: 100%;
  padding: 20px 0;
  background: white;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.section-title-appointment {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 22px;
  font-weight: 600;
  color: #0283f5;
  margin-bottom: 20px;
}

.icon {
  display: inline-block;
}

/* About content */
.about-text {
  line-height: 1.6;
  color: #444;
  font-size: 18px;
}

.about-text ul {
  margin-left: 20px;
  list-style: disc;
}

/* Working Hours */
.hours-table {
  display: flex;
  flex-direction: column;
  gap: 4px; /* Giảm khoảng cách giữa các dòng */
}

.hours-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0; /* Giảm padding để thu gọn chiều cao */
  border-bottom: 1px solid #f3f4f6;
}

.hours-row:last-child {
  border-bottom: none;
}

.day {
  color: #374151;
  font-weight: 500;
  font-size: 16px; /* Thu nhỏ font */
}

.hours {
  color: #6b7280;
  font-size: 16px; /* Thu nhỏ font */
}

/* FAQ */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.faq-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.faq-question {
  width: 100%;
  padding: 16px;
  background: white;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #374151;
  transition: background-color 0.2s;
}

.faq-question:hover {
  background: #f9fafb;
}

.faq-icon {
  font-size: 18px;
  color: #6b7280;
}

.faq-answer {
  padding: 16px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  font-size: 14px;
  color: #6b7280;
}

/* Service list cards */
.service-list-wrapper {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  box-sizing: border-box;
}

.service-card {
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Responsive */
@media (max-width: 1024px) {
  .main-content {
    padding: 24px 16px;
  }
}

@media (max-width: 768px) {
  .hospital-profile {
    flex-direction: column;
    align-items: flex-start;
  }

  .nav-tabs {
    flex-wrap: wrap;
  }

  .service-list-wrapper {
    grid-template-columns: 1fr;
  }
}
