/* ServiceList.css */

.service-subsection {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.service-list-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.service-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
}

.service-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-info h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #000;
}

.service-info .desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

/* Service name container with online tag */
.service-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #333333;
}

.online-tag {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.original-price {
  text-decoration: line-through;
  color: #999;
  margin-left: 4px;
}

.price-highlight {
  color: #10b981;
  font-weight: 500;
  font-size: 20px;
}

.booking-button {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0283f5, #086ce4) !important;
  color: white !important;
  border-radius: 10px;
  padding: 10px 18px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 40px;
  border: none;
  gap: 8px;
}

.booking-button:hover {
  box-shadow: 0 6px 8px rgba(0, 181, 241, 0.3) !important;
  transform: none !important;
  border-color: transparent !important;
  background: linear-gradient(135deg, #0283f5, #086ce4) !important;
  color: white !important;
}

.price-and-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.price-block {
  margin-bottom: 0px;
  padding-top: 20px;
  /* Bỏ margin để canh giữa */
}

.service-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.left-info {
  flex: 1;
  min-width: 200px;
}

.right-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

/* .service-search-box {
  max-width: 400px;
  margin: 0 auto 32px;
  text-align: center;
} */

.service-search-box input {
  width: 100%;
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 15px;
  outline: none;
  transition: border-color 0.3s ease;
}

.service-search-box input:focus {
  border-color: #1890ff;
}

.service-tab-buttons {
  display: flex;
  justify-content: flex-start;
  /* 👈 Canh trái */
  gap: 8px;
  margin: 16px 0 24px;
  flex-wrap: wrap;
}

.service-tab-button {
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  background: #f3f4f6;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: background 0.2s ease;
}

.service-tab-button:hover {
  background: #e5e7eb;
}

.service-tab-button.active {
  background: #0283f5;
  color: white;
}

/* Thêm CSS cho phần đánh giá */
.service-rating {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.star-rating {
  display: inline-flex;
  margin-right: 8px;
}

.star {
  color: #e0e0e0;
  font-size: 24px;
}

.star.filled {
  color: #ffc107;
}

.rating-text {
  font-size: 14px;
  color: #666;
}
