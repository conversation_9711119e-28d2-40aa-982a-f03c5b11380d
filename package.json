{"name": "demo06", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide": "^4.1.4", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@stomp/stompjs": "^7.1.1", "@supabase/supabase-js": "^2.50.0", "antd": "^5.25.4", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "jwt-decode": "^3.1.2", "lucide-react": "^0.514.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-social-login-buttons": "^4.1.1", "react-toastify": "^11.0.5", "recharts": "^2.15.4", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sockjs-client": "^1.6.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}